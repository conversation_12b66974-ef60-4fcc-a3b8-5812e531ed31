import { defineRouting } from "next-intl/routing";

export const LOCALES = ['en', 'zh', 'ja']
export const DEFAULT_LOCALE = 'zh'
export const LOCALE_NAMES: Record<string, string> = {
  'en': "English",
  'zh': "中文",
  'ja': "日本語",
};

export const routing = defineRouting({
  // A list of all locales that are supported
  locales: LOCALES,

  // Used when no locale matches
  defaultLocale: DEFAULT_LOCALE,

  // auto detect locale
  localeDetection: process.env.NEXT_PUBLIC_LOCALE_DETECTION === 'true',

  //to remove the locale prefix from the url
  // localePrefix: 'always',
  localePrefix: 'as-needed',
});

export type Locale = (typeof LOCALES)[number]; 
