import { getRequestConfig } from "next-intl/server";
import { hasLocale } from "next-intl";
import { routing, LOCALES, type Locale } from "./routing";

type Messages = {
  default: Record<string, string>;
};

export default getRequestConfig(async ({ requestLocale }) => {
  const requested = await requestLocale;

  const locale = hasLocale(routing.locales, requested)
    ? requested
    : routing.defaultLocale;

  if (!locale || !isValidLocale(locale)) {
    return {
      locale: routing.defaultLocale,
      messages: ((await import(`./messages/${routing.defaultLocale}.json`)) as Messages ).default
    };
  }

  return {
    locale,
    messages: ((await import(`./messages/${locale}.json`)) as Messages ).default,
  };
});

function isValidLocale(locale: string): locale is Locale {
  return (LOCALES as readonly string[]).includes(locale);
}
