import createMiddleware from 'next-intl/middleware';
import { routing } from 'i18n/routing';
import { auth } from "@/server/auth";
import { NextRequest, NextResponse } from "next/server";

// 创建 next-intl 的 middleware
const intlMiddleware = createMiddleware(routing);

/**
 * @param {NextRequest} request
 */
export async function middleware(request) {
  // 先运行 i18n middleware
  const intlResponse = intlMiddleware(request);
  if (intlResponse) {
    return intlResponse;  // 如果 next-intl middleware 需要返回响应，则返回
  }

  try {
    // 检查用户是否已登录
    const session = await auth();

    // 如果已登录，放行
    if (session?.user) {
      return NextResponse.next();
    }
  } catch (e) {
    console.error(e);
  }

  // 如果未登录，重定向到登录页
  const loginUrl = new URL("/signin", request.url);
  return NextResponse.redirect(loginUrl);
}

export const config = {
  matcher: [
    '/', // 根路径
    '/(en|zh|ja)/:path*', // 带有语言前缀的路径
    '/((?!api|_next|_vercel|.*\\.|favicon.ico).*)', // 其他路径
    '/((?!api|signin|signup|robots.txt|privacy-policy|favicon.ico|_next/static|_next/image|static).*)' // 登录相关路径之外的其他请求
  ]
};
