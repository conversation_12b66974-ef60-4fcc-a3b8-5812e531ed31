const HASH_ALGORITHM = 'SHA-256';
const SALT_LENGTH = 16; // 字节数
const ITERATIONS = 100_000;
const KEY_LENGTH = 32; // 字节数，对应 Node.js 中的 64 hex 字符

/**
 * 生成随机盐值（十六进制字符串）
 */
export function generateSalt(length = SALT_LENGTH): string {
  const salt = new Uint8Array(length);
  crypto.getRandomValues(salt);
  return bufferToHex(salt);
}

/**
 * 使用 Web Crypto API 的 PBKDF2 派生密钥
 */
export async function hashPassword(password: string, saltHex: string): Promise<string> {
  const enc = new TextEncoder();
  const passwordKey = enc.encode(password);
  const salt = hexToBuffer(saltHex);

  const key = await crypto.subtle.importKey(
    'raw',
    passwordKey,
    { name: 'PBKDF2' },
    false,
    ['deriveBits']
  );

  const derivedBits = await crypto.subtle.deriveBits(
    {
      name: 'PBKDF2',
      salt,
      iterations: ITERATIONS,
      hash: HASH_ALGORITHM,
    },
    key,
    KEY_LENGTH * 8 // bits
  );

  return bufferToHex(new Uint8Array(derivedBits));
}

/**
 * 验证密码是否匹配
 */
export async function verifyPassword(password: string, salt: string, hashedPassword: string): Promise<boolean> {
  const hash = await hashPassword(password, salt);
  return hash === hashedPassword;
}

/**
 * 生成盐并加密密码
 */
export async function encryptPassword(password: string): Promise<{ salt: string; hash: string }> {
  const salt = generateSalt();
  const hash = await hashPassword(password, salt);
  return { salt, hash };
}

/**
 * 辅助函数：Uint8Array -> hex 字符串
 */
function bufferToHex(buffer: Uint8Array): string {
  return Array.from(buffer).map(b => b.toString(16).padStart(2, '0')).join('');
}

/**
 * 辅助函数：hex 字符串 -> Uint8Array
 */
function hexToBuffer(hex: string): Uint8Array {
  if (hex.length % 2 !== 0) throw new Error("Invalid hex string");
  const bytes = new Uint8Array(hex.length / 2);
  for (let i = 0; i < hex.length; i += 2) {
    bytes[i / 2] = parseInt(hex.slice(i, i + 2), 16);
  }
  return bytes;
}