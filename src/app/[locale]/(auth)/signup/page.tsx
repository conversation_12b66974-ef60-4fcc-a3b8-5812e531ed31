import { SignupForm } from "@/components/signup-form";
import { api, HydrateClient } from "@/trpc/server";
import { redirect } from "next/navigation";

export default function SignupPage() {
  async function signup(formData: FormData) {
    "use server";
    const email = formData.get("email") as string;
    const password = formData.get("password") as string;

    await api.user.signup({ email, password });

    return redirect("/signin");
  }

  return (
    <HydrateClient>
      <SignupForm action={signup} />
    </HydrateClient>
  )
}