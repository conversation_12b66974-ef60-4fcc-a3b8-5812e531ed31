"use server";

import { LoginForm } from "@/components/login-form"
import { HydrateClient } from "@/trpc/server";
import { auth, signIn } from "@/server/auth";
import { redirect } from "next/navigation";

export default async function LoginPage() {
  const session = await auth();
  if (session?.user) {
    return redirect("/"); // 如果已登录，重定向到首页
  }

  async function signin(formData: FormData) {
    "use server";

    const provider = formData.get("provider") as string;
    await signIn(provider ?? "credentials", formData);

    return redirect("/"); // 登录成功后重定向到首页
  }

  return (
    <HydrateClient>
      <LoginForm action={signin} />
    </HydrateClient>
  )
}
