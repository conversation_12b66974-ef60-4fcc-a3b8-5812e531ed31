import { api } from "@/trpc/server";
import type { TaskPro } from "@/hooks/use-task-query";
import { TaskDetail } from "@/components/task";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";

export default async function TaskPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const task = await api.task.getById({ id }) as TaskPro

  return (
    <section className="bg-gray-50 h-full">
      <nav className="bg-white w-full m-auto h-12">
        <div className="max-w-4xl m-auto h-full flex items-center gap-2">
          <Link href="/tasks">
            <ArrowLeft className="text-gray-500 hover:text-gray-800 cursor-pointer" />
          </Link>
          <h2 className="text-xl text-gray-800 w-full">任务详情</h2>
        </div>
      </nav>
      <div className="w-full max-w-4xl m-auto mt-4 flex-4">
        <TaskDetail task={task} />
      </div>
    </section>
  );
}

