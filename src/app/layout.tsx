import "@/styles/globals.css";

import { type Metadata } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";

import { TRPCReactProvider } from "@/trpc/react";
import { NextIntlClientProvider } from "next-intl";
import { DEFAULT_LOCALE } from "i18n/routing";
import { getMessages } from "next-intl/server";
import { Toaster } from "@/components/ui/sonner";

export const metadata: Metadata = {
  title: "远程遥操作平台",
};

const geist = Geist({
  subsets: ["latin"],
  variable: "--font-geist-sans",
});

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ locale: string }> ;
}>) {
  const { locale } = await params;
  const messages = await getMessages();

  return (
    <html lang={locale || DEFAULT_LOCALE} className={`${geist.variable}`}>
      <body>
        <NextIntlClientProvider locale={locale} messages={messages}>
          <TRPCReactProvider>{children}</TRPCReactProvider>
        </NextIntlClientProvider>
        <Toaster position="top-center" richColors />
      </body>
    </html>
  );
}
