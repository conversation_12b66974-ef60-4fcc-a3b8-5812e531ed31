import { Bell } from "lucide-react";
import { Button } from "../ui/button";

export default function Nav({ length }: { length: number }) {
  return (
    <nav className="bg-white shadow-none border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-4">
        <div className="flex justify-between items-center py-5">
          <div className="flex-1 flex items-center">
            <div className="flex items-center space-x-1">
              <Button className="relative bg-gray-100 rounded-full w-8 h-8 cursor-pointer hover:bg-gray-200">
                <Bell className="w-5 h-5 text-gray-800" />
                {
                  length > 0 &&
                  <span className="absolute bottom-[-4] right-[-10] bg-red-500 w-5 h-5 flex justify-center items-center text-white text-xs rounded-full">{length}</span>
                }
              </Button>
            </div>
            <ul className="ml-10 flex gap-4 text-gray-600">
              <li>tag1</li>
              <li>tag2</li>
              <li>tag3</li>
            </ul>
          </div>
          <div className="flex-1"></div>
          <div className="flex-1 flex items-center justify-end gap-4 text-gray-700">
            <span>tag1</span>
            <span>tag2</span>
          </div>
        </div>
      </div>
    </nav>
  )
}
