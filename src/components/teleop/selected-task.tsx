import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, X } from "lucide-react";
import { useApplication, type ApplyType } from "@/hooks/use-application";
import { Button } from "../ui/button";
import { TaskApplicationState } from "@prisma/client";
import { FormatType, useDate } from "@/hooks/use-date";

export default function SelectTask({ selectedTask }: { selectedTask: ApplyType }) {
  const {
    updateApplication
  } = useApplication({})

  const { formatDate } = useDate()

  async function completeTask() {
    await updateApplication({
      appId: selectedTask.id,
      taskId: selectedTask.taskId,
      state: 'COMPLETED'
    })
  }

  async function handleCancel() {
    await updateApplication({
      appId: selectedTask.id,
      taskId: selectedTask.taskId,
      state: 'CANCELLED'
    })
  }

  return (
    <>
      <div className="bg-white shadow-xl shadow-gray-100 rounded-sm sticky top-25">
        <div className="p-6">
          <div className="flex items-center justify-between mb-1">
            <h2 className="text-2xl font-semibold text-gray-900">{selectedTask.task.title}</h2>
          </div>
          <div className="mb-3 flex items-center gap-2">
            <span className="text-xs text-gray-600">
              申请于 {formatDate(selectedTask.applicationDate!, FormatType.MONTH_DAY)}
            </span>
            {selectedTask.status === TaskApplicationState.IN_PROGRESS && (
              <>
                <span className="text-xs text-gray-600">| 开始时间 {formatDate(selectedTask.task.startTime!, FormatType.CHINESE_DATETIME)}</span>
                <span className="text-xs text-gray-600">| 结束时间 xxxx-xx-xx</span>
              </>
            )}
          </div>

          <div className="flex items-center gap-2">
            <HandCoins className="w-4 h-4 text-gray-600" />
            <span className="text-md text-gray-600">
              {selectedTask.task.budget}
            </span>
          </div>
          <div className="mb-3 flex items-center gap-2">
            <IdCard className="w-4 h-4 text-gray-600" />
            <span className="text-sm text-gray-600">不限经验</span>
          </div>

          <div className="mb-6">
            <h1 className="text-2xl text-gray-800">任务详情</h1>
            <p className="mt-2 pr-2 text-md text-gray-600">
              {selectedTask.task.description}
            </p>
          </div>

          {/* 操作按钮 */}
          <ButtonGroups status={selectedTask.status!} completeTask={completeTask} handleCancel={handleCancel} />
        </div>
        <TestComp />
      </div>
    </>
  )
}

type GroupsProps = {
  status: TaskApplicationState,
  completeTask: () => void,
  handleCancel: () => void
}

function ButtonGroups({ status, completeTask, handleCancel }: GroupsProps) {
  if (status === TaskApplicationState.CANCELLED || status === TaskApplicationState.COMPLETED) return null

  return (
    <div className="flex gap-2 items-center">
      {status === TaskApplicationState.IN_PROGRESS &&
        <Button onClick={completeTask} className="flex items-center cursor-pointer justify-between px-4 py-2 text-white rounded-lg hover:filter hover:brightness-150 transition-colors">
          完成任务
          <ArrowRight className="w-4 h-4" />
        </Button>
      }
      <Button onClick={handleCancel} className="flex items-center cursor-pointer justify-between bg-red-500/80 px-4 py-2 text-white rounded-lg hover:bg-red-500 transition-colors">
        取消任务
        <Ban className="w-4 h-4" />
      </Button>
    </div>
  )
}

function TestComp() {
  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-1">
        <h2 className="text-2xl text-gray-900">评价</h2>
      </div>
      <ul>
        <li>Lorem ipsum dolor sit amet.</li>
        <li>Lorem ipsum dolor sit amet.</li>
        <li>Lorem ipsum dolor sit amet.</li>
      </ul>
    </div>
  )
}
