import { MoreHorizontal, User } from "lucide-react";
import type { ApplyType } from "@/hooks/use-application";

type ClickType = (data: ApplyType) => void

type ItemProps = {
  data: ApplyType,
  clicked: boolean,
  onClick: ClickType,
  getText: (state: ApplyType["status"]) => string,
  getColor: (state: ApplyType["status"]) => string,
}

export default function TaskItem({ data, clicked, onClick, getText, getColor }: ItemProps) {
  return (
    <div
      className="group bg-white rounded-sm border border-gray-100 cursor-pointer transition-all duration-200 data-[clicked=true]:border-primary data-[clicked=false]:hover:shadow-xl data-[clicked=false]:hover:shadow-gray-200"
      data-clicked={clicked}
      onClick={() => onClick(data)}
    >
      <div className="p-6">
        {/* 任务头部 */}
        <div className="flex items-start justify-between mb-2">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <h3 className="text-xl font-semibold text-gray-900 transition-colors duration-200 group-data-[clicked=false]:group-hover:text-primary">{data.task.title}</h3>
              <span className={`p-[1px] rounded-xs text-xs font-medium border ${getColor(data.status)}`}>
                {getText(data.status)}
              </span>
            </div>
            <p className="text-gray-600 text-sm mb-3 line-clamp-2">{data.task.description}</p>
            <span className="text-2xl text-primary">¥{data.task.budget}</span>
          </div>
          <div className="flex items-center space-x-2">
            <button className="p-2 hover:bg-gray-100 rounded-lg">
              <MoreHorizontal className="w-5 h-5 text-gray-400" />
            </button>
          </div>
        </div>
        {/* 任务信息 */}
        <div className="grid grid-cols-2 gap-4 text-sm pt-2 border-t border-dashed">
          <div className="flex items-center text-gray-600">
            <User className="w-4 h-4 mr-2" />
            <span>负责人：{data.applicant.name}</span>
          </div>
        </div>
      </div>
    </div>
  )
}
