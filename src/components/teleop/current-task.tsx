'use client'

import React, { useEffect, useState } from 'react';
import Nav from './nav';
import SelectTask from './selected-task';
import TaskItem from './task-item';
import { Search } from 'lucide-react';
import { useApplication, type ApplyType } from '@/hooks/use-application';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import useStatusGet from "@/hooks/use-state-get";

export default function CurrentTask() {
  const [selectedTask, setSelectedTask] = useState<ApplyType | null>(null);
  const [search, setSearch] = useState<string>('');
  const [clicked, setClicked] = useState<string>('');
  const {
    getStatusText,
    getStatusColor
  } = useStatusGet()

  const { byApplicantId: list, setFilter, isLoading, isFetching } = useApplication({})

  // 等待查询成功且不在加载中
  useEffect(() => {
    if (!isFetching && list.length > 0) {
      const first = list[0];
      if (first) {
        setSelectedTask(first);
        setClicked(first.id);
      } else {
        setSelectedTask(null);
      }
    }
  }, [list, isFetching])

  function handleClick(task: ApplyType) {
    setSelectedTask(task)
    setClicked(task.id);
  }

  function handleFilter(value: string) {
    setFilter(prev => ({
      ...prev,
      status: value
    }));
  }

  function onSearch() {
    setFilter(prev => ({
      ...prev,
      taskTitle: search
    }))
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航栏 */}
      <Nav length={list?.length ?? 0} />
      <Filter searchTerm={search} setSearch={setSearch} handleFilter={handleFilter} onSearch={onSearch} />
      <div className="max-w-7xl mx-auto py-5">
        {isLoading && (
          <>
            <TaskItemSkeleton />
            <TaskItemSkeleton />
            <TaskItemSkeleton />
          </>
        )}
        {(!isLoading && list.length <= 0) && <span className='text-xl text-gray-500'>暂无数据</span>}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 任务列表 */}
          <div className="lg:col-span-1 space-y-6">
            {list?.map(task => (
              <TaskItem
                key={task.id}
                data={task}
                clicked={task.id === clicked}
                onClick={handleClick}
                getText={getStatusText}
                getColor={getStatusColor}
              />
            ))}
          </div>

          {/* 任务详情面板 */}
          <div className="lg:col-span-2">
            {selectedTask && <SelectTask selectedTask={selectedTask} />}
          </div>
        </div>
      </div>
    </div>
  );
};

type FilterProps = {
  searchTerm: string,
  setSearch: (value: string) => void,
  handleFilter: (value: string) => void,
  onSearch: () => void
}

export function Filter({ searchTerm, setSearch, handleFilter, onSearch }: FilterProps) {
  return (
    <section className='sticky top-0 h-20 flex items-center m-auto bg-white'>
      <div className='max-w-7xl w-full m-auto px-2'>
        <div className="flex items-center justify-between space-x-4">
          <div className="relative flex px-2 py-1 gap-2 border-2 border-gray-100 rounded-lg no-focus-ring focus-within:border-primary">
            <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              type="text"
              placeholder="搜索任务..."
              value={searchTerm}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10 pr-4 py-2 border-none bg-transparent shadow-none focus:outline-none"
            />
            <div className="flex items-center">
              <Button className="rounded-sm" size='sm' onClick={onSearch}>搜索</Button>
            </div>
          </div>
          <div className="mt-2 flex gap-2">
            <Select onValueChange={(value) => handleFilter(value)}>
              <SelectTrigger className="w-38" size='default'>
                <SelectValue placeholder="全部状态" className="text-bold" />
              </SelectTrigger>
              <SelectContent align="end">
                <SelectItem value=" ">全部状态</SelectItem>
                <SelectItem value="PENDING">待审核</SelectItem>
                <SelectItem value="IN_PROGRESS">进行中</SelectItem>
                <SelectItem value="CANCELLED">已取消</SelectItem>
                <SelectItem value="EXPIRED">已过期</SelectItem>
                <SelectItem value="COMPLETED">已完成</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
    </section>
  )
}

const TaskItemSkeleton = () => (
  <div className="p-4 bg-white border rounded-lg animate-pulse w-150 h-40 mt-4">
    <div className="h-4 bg-gray-200 rounded w-4/5 mb-3"></div>
    <div className="h-4 bg-gray-200 rounded w-4/5 mb-3"></div>
    <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
    <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
  </div>
);
