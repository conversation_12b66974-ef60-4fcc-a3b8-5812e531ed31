"use client"

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Input } from "../ui/input";
import { Button } from "../ui/button";
import TaskCard from "./task-card";
import { useTaskQuery, type HandleFilterType } from "@/hooks/use-task-query";
import { useSearch } from "@/hooks/use-search";
import { useSticky } from "@/hooks/use-sticky";
import type { FilterBarProps, FilterType } from "./type";

export default function TaskList() {

  const {
    list,
    order,
    handleFilter
  } = useTaskQuery('');

  const handleValChange = (value: 'asc' | 'desc') => handleFilter('order', value)

  return (
    <section>
      <Header onSearch={handleFilter} handleFilter={handleFilter} />
      <TaskCard list={list}>
        <div className="flex px-8 justify-between items-center bg-white h-10">
          <div className="text-gray-600 text-sm">
            {list.length} 任务
          </div>
          <div className="flex items-center gap-4">
            <Select value={order} onValueChange={handleValChange}>
              <SelectTrigger
                className="shadow-none **:data-[slot=select-value]:block **:data-[slot=select-value]:truncate border-none text-md"
                size="sm"
              >
                <SelectValue placeholder="排序方式" className="text-bold" />
              </SelectTrigger>
              <SelectContent className="text-bold">
                <SelectItem value="asc">按时间正序</SelectItem>
                <SelectItem value="desc">按时间倒序</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </TaskCard>
    </section>
  );
}

function Header({ onSearch,handleFilter }: { onSearch: HandleFilterType, handleFilter: HandleFilterType }) {
  const {
    inputValue,
    isSearching,
    handleClick,
    handleKeyDown,
    handleInputChange,
  } = useSearch('title', onSearch);

  const isSticky = useSticky(0);

  const filterList: FilterType[] = [
    {
      label: '佣金价格',
      options:
        [
          { label: '全部', value: ' ' },
          { label: '100-300', value: '100-300' },
          { label: '300-1000', value: '300-1000' },
          { label: '1000-2000', value: '1000-2000' },
        ],
      onChange: async (value: string) => handleFilter('budget', value)
    },
    {
      label: '发布时间',
      options:
        [
          { label: '全部', value: ' ' },
          { label: '今天', value: 'today' },
          { label: '过去一周', value: 'week' },
          { label: '过去一个月', value: 'month' },
        ],
      onChange: async (value: string) => handleFilter('publishedTime', value)
    }
  ]

  return (
    <div className={`sticky z-10 top-0 mt-4 pt-2 bg-white ${isSticky ? "shadow-md border-b border-[#eee]" : ""}`} >
      <div className="max-w-[1400px] mx-auto h-auto">
        <div className="flex justify-between items-center px-2 py-2">
          <h1 className="text-3xl font-semibold flex-1 ">任务列表</h1>
          <div className="flex-2 flex gap-2">
            <Input
              placeholder="remote"
              className="flex-1"
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              disabled={isSearching}
            />
            <Button
              className="m-auto"
              onClick={handleClick}
              disabled={isSearching}
            >
              {isSearching ? '搜索中...' : '搜索'}
            </Button>
          </div>
          <div className="flex-1" />
        </div>
        <div className="sticky flex justify-start w-full my-4">
          <div className="flex gap-4 items-center flex-1">
            {
              filterList.map(item => (
                <FilterBar
                  key={item.label}
                  label={item.label}
                  options={item.options}
                  onChange={item.onChange}
                />
              ))
            }
          </div>
          {/* <div className="flex gap-4 items-center justify-end flex-1">
            <Switch id="airplane-mode" />
            <Label htmlFor="airplane-mode">Pro</Label>
          </div> */}
        </div>
      </div>
    </div>
  );
}

function FilterBar({ label, options, onChange }: FilterBarProps) {
  return (
    <Select onValueChange={onChange}>
      <SelectTrigger className="w-38" size="sm">
        <SelectValue placeholder={label} className="text-bold" />
      </SelectTrigger>
      <SelectContent align="end">
        {options.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
