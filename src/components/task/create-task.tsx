"use client"

import { api } from "@/trpc/react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../ui/sheet";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Textarea } from "../ui/textarea";
import { Label } from "../ui/label";

export default function CreateTask() {

  const utils = api.useUtils();

  const addTask = api.task.createTask.useMutation({
    async onSuccess() {
      // Invalidate cache to trigger data update
      // refetches lists after a task is added
      await utils.task.queryTasks.invalidate();
    },
    onError(error) {
      console.error("添加任务失败:", error);
    },
  });

  const create = async (formData: FormData) => {
    const data = {
      title: formData.get('title') as string,
      description: formData.get('description') as string,
      attachmentUrl: ['https://imgapi.xl0408.top/index.php'],
      budget: Number(formData.get('budget') as string),
    }
    // await addTask.mutateAsync(data)
  };

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button className="cursor-pointer bg-white border border-input text-gray-900 hover:bg-gray-50">
          发布任务
        </Button>
      </SheetTrigger>

      <SheetContent>
        <form action={create} className="h-full flex flex-col">
          <SheetHeader>
            <SheetTitle className="pb-3 border-b border-gray-300">
              任务发布
            </SheetTitle>
          </SheetHeader>
          <div className="grid gap-4 p-4">
            <div className="grid items-center gap-4">
              <Label htmlFor="title" className="text-right">
                任务名称
              </Label>
              <Input id="title" name="title" placeholder="请输入任务名称" />
            </div>
            <div className="grid items-center gap-4">
              <Label htmlFor="budget" className="text-right">
                任务佣金
              </Label>
              <Input id="budget" name="budget" type="number" />
            </div>
            <div className="grid items-center gap-4">
              <Label htmlFor="desc">任务描述</Label>
              <Textarea id="desc" name="description" placeholder="Type your message here." className="h-60" />
            </div>
          </div>

          <SheetFooter className="border-t border-gray-300">
            <SheetClose asChild>
              <div className="flex justify-end">
                <div className="flex-2"></div>
                <Button
                  type="submit"
                  className="flex-1 cursor-pointer"
                >
                  提交
                </Button>
              </div>
            </SheetClose>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  )
}
