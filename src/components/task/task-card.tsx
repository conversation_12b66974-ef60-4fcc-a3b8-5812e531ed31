import { <PERSON>, Heart } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "../ui/card";
import { type TaskPro } from "@/hooks/use-task-query";
import { type ReactElement } from "react";
import Image from "next/image";
import { Avatar, AvatarImage } from "../ui/avatar";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel"
import { useRouter } from "next/navigation";

export default function TaskCard({ list, children }: { list: TaskPro[], children: ReactElement }) {
  const router = useRouter();

  function toDetail(id: string) {
    router.push(`/tasks/${id}`)
  }

  if (!list || list.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <p className="text-gray-500">暂无任务</p>
      </div>
    );
  }

  return (
    <section className="max-w-[1480px] mx-auto">
      <div className="mt-1">
        {children}
        <div className="grid grid-cols-1 gap-4 mx-auto px-4 lg:px-6 xl:grid-cols-5 5xl:grid-cols-5">
          {list.map((task, idx) => (
            <Card key={`${task.id}-${idx}`} className="group bg-transparent border-none shadow-none @container/card mt-6 pt-0 pb-2 gap-1 overflow-hidden max-w-[300px]">
              <CardHeader className="px-0">
                <div className="relative">
                  <Banner covers={task.attachmentUrl} />
                  {/* 右上角收藏标签 */}
                  <div className="absolute cursor-pointer top-3 right-3 px-2 text-gray-700 transition-all duration-200">
                    <Heart
                      className="w-5 h-5 text-gray-400 hover:text-red-500 hover:fill-current transition-colors duration-200"
                    />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="px-4 py-1">
                <CardDescription>
                  <CardTitle>
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="text-lg font-semibold text-gray-900 line-clamp-2 flex-1 mr-2">
                        {task.title}
                      </h3>
                    </div>
                  </CardTitle>
                  <div className="flex items-center mb-2">
                    <div className="mr-2 relative">
                      <Avatar className="h-6 w-6 rounded-full">
                        <AvatarImage src={task.creator.image} alt='cover-img' />
                      </Avatar>
                      <div className="w-2 h-2 bg-green-400 rounded-full mr-1 absolute right-[-3] bottom-[-1]"></div>
                    </div>
                    <div className="flex-1">
                      <p className="text-xs text-gray-600">
                        <span className="text-blue-600 cursor-pointer">{task.creator.name}</span>
                      </p>
                    </div>
                    <div className="text-right flex gap-2 items-center">
                      {/* 评级 */}
                    </div>
                  </div>
                  <p onClick={() => toDetail(task.id)} className="text-sm text-gray-600 mb-2 line-clamp-2 underline-offset-4 hover:underline hover:decoration-gray-400 hover:cursor-pointer">
                    {task.description}
                  </p>
                </CardDescription>
              </CardContent>
              <CardFooter className="flex-col items-start gap-1.5 text-sm px-4">
                <div className="flex justify-between items-end w-full">
                  <div className="flex items-end">
                    <span className="text-2xl font-bold text-primary">
                      ￥ {task.budget}
                    </span>
                    <span className="text-xs text-gray-500 ml-1">/次</span>
                  </div>
                  <div className="flex items-center text-xs text-gray-500">
                    <Calendar className="w-3 h-3 mr-1" />
                    <span>{new Date(task.createdAt).toLocaleDateString('zh-CN')}</span>
                  </div>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}

export function Banner({ covers }: { covers: string[] }) {
  return (
    <div className="relative w-full max-w-xs">
      <Carousel className="w-full">
        <CarouselContent>
          {covers.map((cover, index) => (
            <CarouselItem key={index}>
              <Image
                src={cover}
                alt={`Task image ${index}`}
                className="w-full h-40 object-cover"
                width={100}
                height={40}
              />
            </CarouselItem>
          ))}
        </CarouselContent>
        <div className="opacity-0 group-hover:opacity-100 transition-all duration-200">
          <CarouselPrevious className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 transition-all" />
          <CarouselNext className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 transition-all" />
        </div>
      </Carousel>
    </div>
  )
}
