'use client'

import { Avatar, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import { useApplication } from "@/hooks/use-application";
import { FormatType, useDate } from "@/hooks/use-date";
import type { TaskPro } from "@/hooks/use-task-query";
import { IdCard, Share2, Timer } from "lucide-react";
import Image from "next/image";

export default function TaskDetail({ task }: { task: TaskPro }) {
  const { image, name } = task.creator

  const { formatDate } = useDate()

  const { createApplication } = useApplication({
    id: task.id,
    creatorId: task.creatorId,
  })

  async function handleClick() {
    await createApplication()
  }

  return (
    <div className="flex flex-col p-4 bg-white">
      <div className="flex justify-between">
        <h1 className="text-2xl font-semibold">{task.title}</h1>
        <Button className="flex bg-transparent text-gray-500 border-none shadow-none cursor-pointer hover:bg-gray-100">
          分享
          <Share2 />
        </Button>
      </div>
      <div className="mt-2 border-b pb-4">
        <div className="text-sm text-gray-500">更新于 {formatDate(task.updatedAt, FormatType.MONTH_DAY)}</div>
        <div className="text-sm text-gray-500 mt-2 flex items-center gap-2">
          <Timer className="w-4 h-4" /> 不限时间
        </div>
        <div className="text-sm text-gray-500 mt-2 flex items-center gap-2">
          <IdCard className="w-4 h-4" /> 不限经验 | 需要XXX
        </div>
      </div>

      <div className="mt-4 border-b pb-4">
        <h2 className="text-xl font-semibold">技能</h2>
        <div className="flex gap-2 mt-1">
          <Button className="bg-gray-200 text-gray-700 hover:bg-gray-250" size='sm'>机器人</Button>
          <Button className="bg-gray-200 text-gray-700 hover:bg-gray-250" size='sm'>XXX</Button>
          <Button className="bg-gray-200 text-gray-700 hover:bg-gray-250" size='sm'>XXX</Button>
        </div>
      </div>

      <div className="mt-4 border-b pb-4">
        <h2 className="text-xl font-semibold">工作内容</h2>
        <p className="text-gray-600 mt-2">
          {task.description}
        </p>
      </div>
      <div className="mt-4 border-b pb-4">
        <div className="relative w-full h-100">
          {task.attachmentUrl.map((cover, index) => (
            <Image
              key={index}
              src={cover}
              alt={`Task image ${index}`}
              className="object-cover border-sky-500"
              layout="fill"
            />
          ))}
        </div>
      </div>
      <div className="flex items-center space-x-4 mt-4">
        <div className="flex items-center">
          <Avatar className="h-10 w-10 mr-3 rounded-full">
            <AvatarImage src={image} alt='User Avatar' />
          </Avatar>
          <div>
            <div className="text-md text-gray-800">{name}</div>
            <div className="text-sm text-gray-600">Lorem ipsum dolor sit.</div>
          </div>
        </div>
      </div>
      <div className="mt-6 flex justify-center">
        <Button
          className="text-white border cursor-pointer hover:filter hover:brightness-130 transition-colors duration-200"
          onClick={handleClick}
        >
          申请任务
        </Button>
      </div>
    </div>
  );
}
