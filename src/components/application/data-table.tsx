'use client'

import React, { useState } from 'react';
import {
  createColumn<PERSON><PERSON>per,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { Input } from '../ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { Button } from '../ui/button';
import { useApplication, type ApplyType } from '@/hooks/use-application';
import useStateGet from '@/hooks/use-state-get';
import { IconChevronLeft, IconChevronRight, IconChevronsLeft, IconChevronsRight, IconDotsVertical } from '@tabler/icons-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '../ui/dropdown-menu';
import type { TaskApplicationState } from '@prisma/client';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Checkbox } from '../ui/checkbox';
import { FormatType, useDate } from '@/hooks/use-date';
import { ChevronDown, ChevronsUpDown, ChevronUp } from 'lucide-react';

type ActionType = ({ appId, taskId, state }: { taskId: string, appId: string; state: TaskApplicationState; }) => Promise<void>

interface ActionGroupProps {
  id: string;           // 任务申请的ID
  action: ActionType;   // 用于更新任务申请状态和任务状态的函数
  taskId: string;       // 任务ID，用于更新任务的状态
}

export default function DataTable() {
  const [globalFilter, setGlobalFilter] = useState<string>('');

  const { formatDate } = useDate()

  const {
    list: data,
    updateApplication,
    queryCondition: { order },
    setQueryCondition
  } = useApplication({ })

  const { getStatusText, getStatusColor } = useStateGet()

  function handleValueChange(value: string) {
    setQueryCondition(prev => ({
      ...prev,
      status: value as TaskApplicationState
    }))
  }

  // 创建列辅助器
  const columnHelper = createColumnHelper<ApplyType>();
  // 定义列
  const columns = [
    columnHelper.display({
      id: "select",
      header: ({ table }) => (
        <div className="flex items-center justify-center">
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
            aria-label="Select all"
          />
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex items-center justify-center">
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
          />
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    }),
    columnHelper.accessor('task.title', {
      header: '任务名称',
      cell: info => info.getValue(),
    }),
    columnHelper.accessor('status', {
      header: '状态',
      cell: info => {
        const val = info.getValue()
        return (
          <span
            className={`${getStatusColor(val)} p-1 rounded-sm `}
          >
            {getStatusText(val)}
          </span>
        )
      }
    }),
    columnHelper.accessor('task.budget', {
      header: '佣金',
      cell: info => info.getValue()
    }),
    columnHelper.accessor('applicationDate', {
      header: () => {
        function click() {
          setQueryCondition(prev => ({
            ...prev,
            order: order === 'desc' ? 'asc' : 'desc'
          }))
        }
        return (
          <button
            className="flex items-center gap-2 hover:text-primary cursor-pointer"
            onClick={click}
          >
            申请日期
            {order === "asc" ? (
              <ChevronUp className="h-4 w-4" />
            ) : order === "desc" ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronsUpDown className="h-4 w-4" />
            )}
          </button>
        )
      },
      cell: info => formatDate(info.getValue()!, FormatType.CHINESE_DATETIME),
      enableSorting: true,
    }),
    columnHelper.accessor('applicant.name', {
      header: '申请人',
      cell: info => info.getValue(),
    }),
    columnHelper.accessor('taskPublisher.name', {
      header: '发布人',
      cell: info => info.getValue()
    }),
    columnHelper.display({
      id: 'actions',
      header: '操作',
      cell: info => {
        const rowId = info.row.original.id
        const taskId = info.row.original.taskId

        return <ActionGroup taskId={taskId} id={rowId} action={updateApplication} />
      }
    })
  ];

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onGlobalFilterChange: setGlobalFilter,
    getPaginationRowModel: getPaginationRowModel(),
    state: {
      globalFilter,
    },
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },
  });

  return (
    <div className="w-full mx-auto px-6">
      <div className='my-2 flex flex-wrap items-center justify-between space-y-2'>
        <div>
          <h2 className='text-2xl font-bold'>申请管理</h2>
          <p className='text-muted-foreground'>
            Manage your users and their roles here.
          </p>
        </div>
      </div>
      {/* 全局搜索 */}
      <div className="flex items-center py-4 gap-4">
        <Input
          placeholder="搜索所有列..."
          value={globalFilter ?? ''}
          onChange={(event) => setGlobalFilter(event.target.value)}
          className="max-w-sm"
        />
        <div className="flex gap-2">
          <Select onValueChange={(value) => handleValueChange(value)}>
            <SelectTrigger className="w-38" size='default'>
              <SelectValue placeholder="全部状态" className="text-bold" />
            </SelectTrigger>
            <SelectContent align="end">
              <SelectItem value=" ">全部状态</SelectItem>
              <SelectItem value="PENDING">待审核</SelectItem>
              <SelectItem value="IN_PROGRESS">进行中</SelectItem>
              <SelectItem value="CANCELLED">已取消</SelectItem>
              <SelectItem value="EXPIRED">已过期</SelectItem>
              <SelectItem value="COMPLETED">已完成</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* 表格 */}
      <div className='space-y-4'>
        <div className='overflow-hidden rounded-md border'>
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    没有找到结果
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* 分页控件 */}
      <div className="flex items-center justify-between px-4 mt-4">
        <div className="text-muted-foreground hidden flex-1 text-sm lg:flex">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="flex w-full items-center gap-8 lg:w-fit">
          <div className="hidden items-center gap-2 lg:flex">
            <Label htmlFor="rows-per-page" className="text-sm font-medium">
              Rows per page
            </Label>
            <Select
              value={`${table.getState().pagination.pageSize}`}
              onValueChange={(value) => {
                table.setPageSize(Number(value))
              }}
            >
              <SelectTrigger size="sm" className="w-20" id="rows-per-page">
                <SelectValue
                  placeholder={table.getState().pagination.pageSize}
                />
              </SelectTrigger>
              <SelectContent side="top">
                {[10, 20, 30, 40, 50].map((pageSize) => (
                  <SelectItem key={pageSize} value={`${pageSize}`}>
                    {pageSize}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex w-fit items-center justify-center text-sm font-medium">
            Page {table.getState().pagination.pageIndex + 1} of{" "}
            {table.getPageCount()}
          </div>
          <div className="ml-auto flex items-center gap-2 lg:ml-0">
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only">Go to first page</span>
              <IconChevronsLeft />
            </Button>
            <Button
              variant="outline"
              className="size-8"
              size="icon"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only">Go to previous page</span>
              <IconChevronLeft />
            </Button>
            <Button
              variant="outline"
              className="size-8"
              size="icon"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only">Go to next page</span>
              <IconChevronRight />
            </Button>
            <Button
              variant="outline"
              className="hidden size-8 lg:flex"
              size="icon"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only">Go to last page</span>
              <IconChevronsRight />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};


function ActionGroup({ id, action, taskId }: ActionGroupProps) {
  async function resolve() {
    await action({
      appId: id,
      taskId: taskId,
      state: 'IN_PROGRESS'
    })
  }

  async function reject() {
    await action({
      appId: id,
      taskId: taskId,
      state: 'CANCELLED'
    })
  }

  return (
    <div className="flex space-x-2">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="data-[state=open]:bg-muted text-muted-foreground flex size-8"
            size="icon"
          >
            <IconDotsVertical />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-32">
          <DropdownMenuItem onClick={resolve}>通过</DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem variant="destructive" onClick={reject}>拒绝</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
