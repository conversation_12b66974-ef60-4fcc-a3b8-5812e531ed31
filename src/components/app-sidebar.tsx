"use client"

import {
  IconDashboard,
  IconDatabase,
  IconFileDescription,
  IconFileWord,
  IconFolder,
  IconHelp,
  IconInnerShadowTop,
  IconListDetails,
  IconReport,
  IconSearch,
  IconSettings,
  IconUsers,
  IconClipboardCheck,
  IconCirclePlus,
  IconUserPlus,
  IconChecklist
} from "@tabler/icons-react"

import { NavDocuments } from "@/components/nav-documents"
import { NavMain } from "@/components/nav-main"
import { NavSecondary } from "@/components/nav-secondary"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import Link from "next/link"
import { NavUser } from "./nav-user"
import { Suspense } from "react"

// const NavUser = React.lazy(() => import("@/components/nav-user").then(module => ({ default: module.NavUser })))

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const data = {
    navMain: [
      {
        title: "首页",
        url: "/",
        icon: IconDashboard,
      },
      {
        title: "任务",
        url: "/tasks",
        icon: IconListDetails,
      },
      {
        title: "项目",
        url: "/projects",
        icon: IconFolder,
      },
      {
        title: "团队",
        url: "/teams",
        icon: IconUsers,
      },
    ],
    navManager: [
      {
        name: "发布任务",
        url: "/publish-task",
        icon: IconCirclePlus,
      },
      {
        name: "我的发布",
        url: "/my-published",
        icon: IconUserPlus,
      },
      {
        name: '申请管理',
        url: '/application-review',
        icon: IconChecklist
      }
    ],
    navSecondary: [
      {
        title: "我的账户",
        url: "/account",
        icon: IconSettings,
      },
      {
        title: "绩效统计",
        url: "/account/statistics",
        icon: IconDatabase,
      },
      {
        title: "收益提现",
        url: "/account/withdraw",
        icon: IconSearch,
      },
    ],
    navQualifications: [
      {
        name: "实名认证",
        url: "/qualifications/real-name",
        icon: IconUsers,
      },
      {
        name: "技能培训",
        url: "/qualifications/training",
        icon: IconReport,
      },
      {
        name: "资格认定",
        url: "/qualifications/certification",
        icon: IconFileWord,
      },
    ],
    documents: [
      {
        name: "快速开始",
        url: "/docs/quick-start",
        icon: IconFileDescription,
      },
      {
        name: "操作指南",
        url: "/docs/guidelines",
        icon: IconInnerShadowTop,
      },
      {
        name: "常见问题",
        url: "/docs/faq",
        icon: IconHelp,
      },
    ],
  }

  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <Link href="/">
                <span className="text-base">艾欧远程遥操作平台</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavDocuments title="任务管理" items={data.navManager} />
        <NavDocuments title="操作资质" items={data.navQualifications} />
        <NavDocuments title="使用帮助" items={data.documents} />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <Suspense>
          <NavUser />
        </Suspense>
      </SidebarFooter>
    </Sidebar>
  )
}
