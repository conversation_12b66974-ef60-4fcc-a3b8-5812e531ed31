'use client'

import { useEffect, useRef } from 'react';
import lottie from 'lottie-web';
import robotJson from '@/app/robot.json'

export default function AwaitDev() {
  const animationContainer = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!animationContainer.current) return;

    // 加载Lottie动画
    const animation = lottie.loadAnimation({
      container: animationContainer.current,
      renderer: 'svg',
      loop: true,
      autoplay: true,
      animationData: robotJson
    });

    // 销毁动画实例（清理）
    return () => {
      animation.destroy();
    };
  }, []);

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100%',
      padding: '20px'
    }}>
      <div 
        className="animation-container" 
        style={{ 
          width: '400px', 
          height: '400px',
          marginBottom: '20px'
        }} 
        ref={animationContainer}
      />
      <p style={{
        fontSize: '18px',
        color: '#666',
        textAlign: 'center',
        margin: '0',
        fontFamily: 'Arial, sans-serif'
      }}>
        功能开发中，敬请期待!
      </p>
    </div>
  );
}
