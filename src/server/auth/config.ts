import { PrismaAdapter } from "@auth/prisma-adapter";
import { type DefaultSession, type NextAuthConfig, type User } from "next-auth";
import Cred<PERSON>sProvider from "next-auth/providers/credentials";
import { db } from "@/server/db";
import { verifyPassword } from "@/lib/secure";
import FeishuProvider from "./providers/FeishuProvider";

/**
 * Module augmentation for `next-auth` types. Allows us to add custom properties to the `session`
 * object and keep type safety.
 *
 * @see https://next-auth.js.org/getting-started/typescript#module-augmentation
 */
declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      // ...other properties
      // role: UserRole;
    } & DefaultSession["user"];
  }
}

/**
 * Options for NextAuth.js used to configure adapters, providers, callbacks, etc.
 *
 * @see https://next-auth.js.org/configuration/options
 */
export const authConfig = {
  providers: [
    // DiscordProvider,
    CredentialsProvider({
      id: "credentials",
      // The name to display on the sign in form (e.g. "Sign in with...")
      name: "Credentials",
      // `credentials` is used to generate a form on the sign in page.
      // You can specify which fields should be submitted, by adding keys to the `credentials` object.
      // e.g. domain, username, password, 2FA token, etc.
      // You can pass any HTML attribute to the <input> tag through the object.
      credentials: {
        email: { label: "Email", type: "text", placeholder: "<EMAIL>" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials): Promise<User | null> {
        const { email, password } = credentials || {};
        const user = await db.user.findUnique({
          where: {
            email: email as string,
          },
        });
        if (!user) {
          throw new Error("Email not found");
        }
        if (!user.password || !user.passwordSlat) {
          throw new Error("User does not have a password set");
        }
        
        // 验证密码
        const isPasswordValid = await verifyPassword(
          password as string,
          user.passwordSlat,
          user.password
        );
        if (!isPasswordValid) {
          throw new Error("Invalid email or password");
        }

        return {
          id: user.id,
          name: user.name,
          email: user.email,
          image: user.image,
        };
      }
    }),

    FeishuProvider,

    /**
     * ...add more providers here.
     *
     * Most other providers require a bit more work than the Discord provider. For example, the
     * GitHub provider requires you to add the `refresh_token_expires_in` field to the Account
     * model. Refer to the NextAuth.js docs for the provider you want to use. Example:
     *
     * @see https://next-auth.js.org/providers/github
     */
  ],

  // 必须使用JWT存储,不能使用数据库存储会话,否则在中间件无法读取到会话
  session: {
    strategy: "jwt",
  },

  adapter: PrismaAdapter(db),
  callbacks: {
    async jwt({ token, user }) {
      // user 只在首次登录时存在
      if (user) {
        token.id = user.id; // 把 user.id 保存进 token
      }
      return token;
    },
    session: ({ session, token }) => {
      // 把 token 中的信息注入到 session.user
      if (token?.id && session.user) {
        session.user.id = token.id as string;
      }
      return session;
    },
  },

  secret: process.env.NEXTAUTH_SECRET,

  // fix UntrustedHost: Host must be trusted.
  trustHost: true,

} satisfies NextAuthConfig;
