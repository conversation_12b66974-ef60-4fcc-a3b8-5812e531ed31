import type { Provider } from "next-auth/providers";

interface FeishuResponse {
  code: number;
  data: {
    open_id: string;
    name: string;
    en_name?: string;
    enterprise_email?: string;
    email?: string;
    avatar_url: string;
  }
}

export default {
  id: 'feishu',
  name: '<PERSON><PERSON><PERSON>',
  type: 'oauth',
  checks: ['state'],
  authorization: {
    url: 'https://accounts.feishu.cn/open-apis/authen/v1/authorize',
    params: {
      response_type: 'code',
      scope: '',
    },
  },
  userinfo: {
    url: 'https://open.feishu.cn/open-apis/authen/v1/user_info',
  },
  token: {
    url: 'https://open.feishu.cn/open-apis/authen/v2/oauth/token',
  },
  // authorization: {
  //   url: 'https://open.feishu.cn/open-apis/authen/v1/index',
  //   params: {
  //     redirect_uri: process.env.NEXTAUTH_URL + '/api/auth/callback/feishu',
  //     app_id: process.env.NEXTAUTH_FEISHU_ID,
  //   },
  // },
  // token: {
  //   url: 'https://open.feishu.cn/open-apis/authen/v1/access_token',
  //   params: {
  //     grant_type: 'authorization_code',
  //     redirect_uri: process.env.NEXTAUTH_URL + '/api/auth/callback/feishu',
  //   }
  // },
  // userinfo: {
  //   url: 'https://open.feishu.cn/open-apis/authen/v1/user_info',
  // },
  clientId: process.env.NEXTAUTH_FEISHU_ID,
  clientSecret: process.env.NEXTAUTH_FEISHU_SECRET,
  profile(res: FeishuResponse) {
    if (res.code !== 0) {
      throw new Error(`Feishu authentication failed: ${res.code}`);
    }
    const profile = res.data;
    return {
      id: profile.open_id,
      name: profile.name,
      email: profile.enterprise_email ?? profile.email ?? null,
      image: profile.avatar_url,
    };
  },
} satisfies Provider;