import { z } from "zod";
import {
  createTR<PERSON>Router,
  publicProcedure,
} from "@/server/api/trpc";
import { type Task } from "@prisma/client";
import { buildWhereCondition } from '../utils'

export const taskRouter = createTRPCRouter({
  getById: publicProcedure
    .input(z.object({
      id: z.string()
    }))
    .query(async ({ ctx, input }) => {
      const { id } = input
      const tasks = await ctx.db.task.findUnique({
        where: { id },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              role: true,
              image: true
            },
          },
        },
      });
      return tasks as Task
    }),

  createTask: publicProcedure
    .input(z.object({
      title: z.string(),
      description: z.string(),
      attachmentUrl: z.tuple([z.string()]),
      budget: z.number()
    }))
    .mutation(async ({ ctx, input }) => {
      const task = ctx.db.task.create({
        data: {
          title: input.title,
          description: input.description,
          attachmentUrl: input.attachmentUrl,
          budget: input.budget,
          creator: {
            connect: {
              id: ctx.session?.user.id
            }
          }
        },
      });

      return task
    }),

  queryTasks: publicProcedure
    .input(z.object({
      title: z.string().optional(),
      order: z.string().optional(),
      budget: z.string().optional(),
      publishedTime: z.string().optional(),
    }))
    .query(async ({ ctx, input }) => {
      const whereCondition = buildWhereCondition(input)

      const order = input.order === 'asc' ? 'asc' : 'desc';
      try {
        const tasks = await ctx.db.task.findMany({
          where: whereCondition,
          include: {
            creator: {
              select: {
                id: true,
                name: true,
                role: true,
                image: true
              },
            },
          },
          orderBy: { createdAt: order },
        });

        return tasks;
      } catch (error) {
        console.error('查询任务失败:', error);
        throw new Error('查询任务失败');
      }
    }),

  updateTask: publicProcedure
    .input(z.object({
      id: z.string(),
      updates: z.record(z.string(), z.any()),
    }))
    .mutation(async ({ ctx, input }) => {
      return ctx.db.task.update({
        where: { id: input.id },
        data: input.updates,
      });
    }),
})
