import { z } from "zod";
import {
  createTRPCRouter,
  publicProcedure,
} from "@/server/api/trpc";
import { TaskApplicationState } from "@prisma/client";

const include = {
  applicant: {
    select: {
      id: true,
      name: true
    }
  },
  taskPublisher: {
    select: {
      id: true,
      name: true
    }
  },
  task: {
    select: {
      title: true,
      description: true,
      budget: true,
      status: true,
      createdAt: true,
      startTime: true
    }
  }
}

export const applicationRouter = createTRPCRouter({
  // 创建申请
  createApplication: publicProcedure
    .input(z.object({
      applicant: z.string(),
      taskId: z.string(),
      taskPublisherId: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const applicant = input.applicant

      if (!applicant) {
        throw new Error('申请人id不能为空')
      }

      return await ctx.db.taskApplication.create({
        data: {
          taskId: input.taskId,
          applicantId: applicant,
          taskPublisherId: input.taskPublisherId,
          status: TaskApplicationState.PENDING,
        }
      })
    }),

  // 获取申请列表
  getApplications: publicProcedure
    .input(z.object({
      status: z.string().optional(),
      order: z.enum(['asc', 'desc']).default('desc'),
    }))
    .query(async ({ ctx, input }) => {
      const order = input.order === 'asc' ? 'asc' : 'desc';
      if (!ctx.db) {
        throw new Error('Database client is not available in context');
      }
      return await ctx.db.taskApplication
        .findMany({
          where:{
             ...(input.status?.trim() !== '' && {
              status: input.status as TaskApplicationState
            })
          },
          include,
          orderBy: { createdAt: order }
        })
    }),

  // 根据任务id和发布者id获取申请信息
  getByTaskId: publicProcedure
    .input(z.object({
      taskId: z.string(),
      publisherId: z.string()
    }))
    .query(async ({ ctx, input }) => {
      return await ctx.db.taskApplication.findFirst({
        where: {
          taskId: input.taskId,
          taskPublisherId: input.publisherId,
        }
      })
    }),

  // 更新申请信息
  updateApplication: publicProcedure
    .input(z.object({
      id: z.string(),
      updates: z.record(z.string(), z.any()),
    }))
    .mutation(async ({ ctx, input }) => {
      return ctx.db.taskApplication.update({
        where: { id: input.id },
        data: input.updates,
      });
    }),
  // 获取用户申请列表
  getByApplicantId: publicProcedure
    .input(z.object({
      applicantId: z.string(),
      taskTitle: z.string().optional(),
      status: z.string().optional(),
      order: z.enum(['asc', 'desc']).default('desc'),
      sortBy: z.enum(['updatedAt', 'createdAt']).default('updatedAt')
    }))
    .query(async ({ ctx, input }) => {
      const order = input.order === 'asc' ? 'asc' : 'desc';

      // 支持多种排序字段
      const getSortField = (sortBy: string) => {
        const sortOptions: Record<string, object> = {
          'updatedAt': { updatedAt: order },
          'createdAt': { createdAt: order }
        };
        // 默认按修改时间
        return sortOptions[sortBy] ?? { updatedAt: order };
      };

      return ctx.db.taskApplication
        .findMany({
          where: {
            applicantId: input.applicantId,
            task: {
              title: {
                contains: input.taskTitle,
                mode: 'insensitive'
              }
            },
            ...(input.status?.trim() !== '' && {
              status: input.status as TaskApplicationState
            })
          },
          include,
          orderBy: getSortField(input.sortBy || 'updatedAt')
        })
    })
})
