import { z } from "zod";

import {
  createTRPCRouter,
  publicProcedure,
} from "@/server/api/trpc";
import { generateSalt, hashPassword } from "@/lib/secure";

export const userRouter = createTRPCRouter({
  hello: publicProcedure
    .input(z.object({ text: z.string() }))
    .query(({ input }) => {
      return {
        greeting: `Hello ${input.text}`,
      };
    }),

  // 用户注册
  signup: publicProcedure
    .input(z.object({ email: z.string().email(), password: z.string().min(6) }))
    .mutation(async ({ ctx, input }) => {
      const name = input.email.split("@")[0] ?? 'Guest'; // 使用邮箱前缀作为用户名
      
      // 对密码进行加盐哈希
      const passwordSlat = generateSalt();
      const hashedPassword = await hashPassword(input.password, passwordSlat);
      
      const user = await ctx.db.user.create({
        data: {
          name, // 使用邮箱前缀作为用户名
          role: "USER",
          email: input.email,
          password: hashedPassword,
          passwordSlat,
        },
      });
      return user;
    }),

});
