const now = new Date();
const timeMap = new Map<string, { gte: Date }>([
  ['today', { gte: new Date(now.setHours(0, 0, 0, 0)) }], // 今天
  ['week', { gte: new Date(now.setDate(now.getDate() - 7)) }], // 过去一周
  ['month', { gte: new Date(now.setMonth(now.getMonth() - 1)) }], // 过去一个月
]);

export function buildWhereCondition(input: {
  title?: string;
  budget?: string;
  publishedTime?: string;
}) {
  const whereCondition = {
    title: {},
    budget: {},
    createdAt: {},
  };

  // 处理标题过滤
  if (input.title) {
    whereCondition.title = {
      contains: input.title,
      mode: 'insensitive' as const,
    };
  }

  // 处理价格范围
  if (input.budget) {
    const [minPrice, maxPrice] = input.budget.split('-').map(Number);
    if (maxPrice) {
      whereCondition.budget = {
        gte: minPrice,
        lte: maxPrice,
      };
    } else {
      whereCondition.budget = {
        gte: minPrice,
      };
    }
  }

  // 处理发布时间
  if (input.publishedTime) {
    const dateCondition = timeMap.get(input.publishedTime);

    if (dateCondition) {
      whereCondition.createdAt = dateCondition;
    }
  }

  return whereCondition;
}


