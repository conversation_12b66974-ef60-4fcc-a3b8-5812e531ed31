import { api } from "@/trpc/react";
import { TaskApplicationState, TaskStatus, type TaskApplication } from "@prisma/client";
import { keepPreviousData } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

export interface ApplyType extends TaskApplication {
  applicant: {
    id: string,
    name: string
  },
  taskPublisher: {
    id: string,
    name: string
  },
  task: {
    title: string,
    description: string,
    budget: number,
    status: string,
    createdAt: Date,
    startTime: Date | null
  }
}

export type QueryTypeByApplicantId = {
  applicantId: string,
  taskTitle?: string,
  status?: string,
  order: 'asc' | 'desc',
  sortBy: 'updatedAt' | 'createdAt'
}

type ApplicationProps = { id?: string, creatorId?: string }

/**
 * 任务申请钩子
 * @param id 任务 id
 * @param creatorId 申请人 id
 * @returns 
 */
export const useApplication = ({ id, creatorId }: ApplicationProps) => {

  const { data, status: sessionStatus } = useSession();

  const user = data?.user ?? { id: '' }

  const utils = api.useUtils()

  const [filter, setFilter] = useState<QueryTypeByApplicantId>({
    applicantId: user.id,
    order: 'desc',
    sortBy: 'updatedAt'
  })

  useEffect(() => {
    if (sessionStatus === 'authenticated' && user.id) {
      setFilter(prevFilter => ({
        ...prevFilter,
        applicantId: user.id
      }));
    }
  }, [user.id, sessionStatus]);

  const [queryCondition, setQueryCondition] = useState<{ order: 'desc' | 'asc', status?: TaskApplicationState }>({
    order: 'desc'
  })

  // 更新任务状态
  const { mutateAsync: updateTask } = api.task.updateTask.useMutation()

  // 创建任务申请
  const { mutateAsync } = api.application.createApplication.useMutation()

  // 查询用户是否存在同一个任务的申请信息
  const { data: exists } = api.application.getByTaskId.useQuery({
    taskId: id!,
    publisherId: creatorId!
  }, {
    enabled: !!(id && creatorId),
    select: (data) => {
      const activeStatuses = [
        TaskApplicationState.PENDING,
        TaskApplicationState.IN_PROGRESS
      ] as TaskApplicationState[];

      return data?.status ? !activeStatuses.includes(data.status) : false;
    }
  })

  // 获取所有的申请信息
  const queryAll = api.application.getApplications.useQuery({ ...queryCondition }, {
    enabled: true,
    refetchOnWindowFocus: false,
    placeholderData: keepPreviousData,
  })

  // 更新申请状态
  const { mutateAsync: updateApplyAsync } = api.application.updateApplication.useMutation()

  // 根据 申请人id 查询
  const applications = api.application.getByApplicantId.useQuery({
    ...filter
  }, {
    enabled: !!filter.applicantId,
    refetchOnWindowFocus: false,
    placeholderData: keepPreviousData,
  })

  // 创建任务申请
  async function createApplication() {
    try {
      // 如果查询到已有记录，阻止继续创建
      if (!exists) {
        toast.warning("您已经申请过此任务!");
        return;
      }

      await mutateAsync({
        applicant: user.id,
        taskId: id!,
        taskPublisherId: creatorId!
      })
      // 添加申请成功后，改变任务的状态和接单人的Id
      await updateTask({
        id: id!,
        updates: {
          status: TaskStatus.IN_PROGRESS,
          assigneeId: user.id,
        }
      })
      toast.success("申请成功!");
    } catch (e) {
      console.error(e);
      toast.error("申请失败");
    }
    await invalidate()
  }

  const taskStateMap = new Map<TaskApplicationState, object>([
    [TaskApplicationState.CANCELLED, { status: 'PENDING', assigneeId: null, startTime: null }],
    [TaskApplicationState.EXPIRED, { status: 'PENDING', assigneeId: null, startTime: null }],
    [TaskApplicationState.IN_PROGRESS, { status: 'IN_PROGRESS', startTime: new Date() }],
    [TaskApplicationState.COMPLETED, { status: 'COMPLETED' }],
  ])

  // 更新任务状态

  async function updateApplication({ appId, state, taskId }:
    { appId: string, taskId: string, state: TaskApplicationState }) {
    await updateApplyAsync({
      id: appId,
      updates: {
        status: state,
      }
    })

    const taskUpdates = taskStateMap.get(state);

    await updateTask({
      id: taskId,
      updates: taskUpdates!
    })
    await invalidate()
  }

  // 使缓存失效，触发更新
  async function invalidate() {
    await utils.application.invalidate()
    await utils.task.invalidate()
  }

  return {
    createApplication,
    updateApplication,
    setFilter,
    byApplicantId: (applications.data ?? []) as ApplyType[],
    isFetching: applications.isFetching,
    isLoading: applications.isLoading,
    list: (queryAll.data ?? []) as ApplyType[],
    queryCondition,
    setQueryCondition,
  }
}
