import { useState } from 'react';
import { api } from "@/trpc/react";
import type { Task } from '@prisma/client';
import { keepPreviousData } from '@tanstack/react-query';

export interface TaskPro extends Task {
  creator: { id: string; name: string; role: string, image: string };
}

export type HandleFilterType = (key: string, value: string) => Promise<void>

export function useTaskQuery(initialSearchValue = '', initialOrder: 'asc' | 'desc' = 'desc') {
  const [filters, setFilters] = useState({
    title: initialSearchValue,
    order: initialOrder,
    budget: '',
    publishedTime: '',
  });

  const queryResult = api.task.queryTasks.useQuery(
    { ...filters },
    {
      enabled: true,
      refetchOnWindowFocus: false,
      placeholderData: keepPreviousData,
    }
  );

  const handleFilter = async (key: string, value: string) => {
    setFilters(filter => ({
      ...filter,
      [key]: value
    }));
  }

  return {
    list: (queryResult.data ?? []) as TaskPro[],
    order: filters.order,
    setFilters,
    handleFilter
  };
};
