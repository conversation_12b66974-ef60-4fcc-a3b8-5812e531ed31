export enum FormatType {
  DATE = 'yyyy-mm-dd',
  TIME = 'HH:mm:ss',
  DATETIME = 'yyyy-mm-dd HH:mm:ss',
  MONTH_DAY = 'mm-dd',
  YEAR_MONTH = 'yyyy-mm',
  CHINESE_DATE = 'yyyy年mm月dd日',
  CHINESE_DATETIME = 'yyyy年mm月dd日 HH:mm:ss'
}

// 格式映射表
const formatMap = new Map<FormatType, (parts: DateParts) => string>([
  [FormatType.DATE, ({ year, month, day }) => `${year}-${month}-${day}`],
  [FormatType.TIME, ({ hour, minute, second }) => `${hour}:${minute}:${second}`],
  [FormatType.DATETIME, ({ year, month, day, hour, minute, second }) =>
    `${year}-${month}-${day} ${hour}:${minute}:${second}`],
  [FormatType.MONTH_DAY, ({ month, day }) => `${month}-${day}`],
  [FormatType.YEAR_MONTH, ({ year, month }) => `${year}-${month}`],
  [FormatType.CHINESE_DATE, ({ year, month, day }) => `${year}年${month}月${day}日`],
  [FormatType.CHINESE_DATETIME, ({ year, month, day, hour, minute, second }) =>
    `${year}年${month}月${day}日 ${hour}:${minute}:${second}`]
]);

interface DateParts {
  year: string;
  month: string;
  day: string;
  hour: string;
  minute: string;
  second: string;
}

function formatDate(value: Date | string | number, format: FormatType = FormatType.DATE): string {
  try {
    const date = new Date(value);

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.warn('Invalid date provided to useDate hook');
      return '';
    }

    // 使用 Intl.DateTimeFormat 格式化日期
    const formattedDate = new Intl.DateTimeFormat('zh-CN', {
      timeZone: 'Asia/Shanghai',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).format(date);

    // 解析格式化后的日期字符串
    const parts = /(\d{4})\/(\d{2})\/(\d{2})\s+(\d{2}):(\d{2}):(\d{2})/.exec(formattedDate);

    if (!parts) {
      console.warn('Failed to parse formatted date');
      return '';
    }

    const dateParts: DateParts = {
      year: date.getFullYear().toString(),
      month: (date.getMonth() + 1).toString().padStart(2, '0'),
      day: date.getDate().toString().padStart(2, '0'),
      hour: date.getHours().toString().padStart(2, '0'),
      minute: date.getMinutes().toString().padStart(2, '0'),
      second: date.getSeconds().toString().padStart(2, '0')
    };

    // 根据格式类型返回相应的字符串
    const formatter = formatMap.get(format);
    if (!formatter) {
      console.warn(`Unsupported format type: ${format}`);
      return '';
    }

    return formatter(dateParts);
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
}

export function useDate(){
  return { formatDate }
}



