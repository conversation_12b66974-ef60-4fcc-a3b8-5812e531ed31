import { useState, useCallback } from 'react';
import type { HandleFilterType } from './use-task-query';

export const useSearch = (key: string, onSearch: HandleFilterType) => {
  const [inputValue, setInputValue] = useState('');
  const [isSearching, setIsSearching] = useState(false);

  const immediateSearch = useCallback(async (value?: string) => {
    const searchValue = value ?? inputValue;
    
    setIsSearching(true);
    try {
      await onSearch(key, searchValue.trim());
    } finally {
      setIsSearching(false);
    }
  }, [onSearch, inputValue, key]);

  const handleClick = async () => {
    await immediateSearch(inputValue);
  };

  const handleKeyDown = async (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      await immediateSearch(e.currentTarget.value);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
  };

  return {
    inputValue,
    setInputValue,
    isSearching,
    immediateSearch,
    handleClick,
    handleKeyDown,
    handleInputChange,
  };
};
