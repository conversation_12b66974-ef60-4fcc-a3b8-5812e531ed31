import type { ApplyType } from "./use-application";

export default function useStateGet() {
  const statusMap = new Map([
    ['PENDING', { text: '待审核', color: 'bg-blue-100 text-blue-800 border-blue-200' }],
    ['IN_PROGRESS', { text: '进行中', color: 'bg-green-100 text-green-800 border-green-200' }],
    ['COMPLETED', { text: '已完成', color: 'bg-purple-100 text-purple-800 border-purple-200' }],
    ['EXPIRED', { text: '已过期', color: 'bg-orange-100 text-orange-800 border-orange-200' }],
    ['CANCELLED', { text: '已取消', color: 'bg-gray-100 text-gray-800 border-gray-200' }],
  ]);

  const taskStatusMap = new Map([
    [ 'PENDING', { text: '待接单', color: 'bg-blue-100 text-blue-800 border-blue-200' } ],
    [ 'IN_PROGRESS', { text: '进行中', color: 'bg-green-100 text-green-800 border-green-200' } ],
    [ 'COMPLETED', { text: '已完成', color: 'bg-purple-100 text-purple-800 border-purple-200' } ],
    [ 'CANCELLED', { text: '已取消', color: 'bg-gray-100 text-gray-800 border-gray-200' } ],
  ])

  const getStatusColor = (status: ApplyType['status']): string => {
    return statusMap.get(status!)?.color ?? 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const getStatusText = (status: ApplyType['status']): string => {
    return statusMap.get(status!)?.text ?? '未知';
  };

  const getTaskStatusColor = (status: ApplyType['status']): string => {
    return taskStatusMap.get(status!)?.color ?? 'bg-gray-100 text-gray-800 border-gray-200';
  };
  const getTaskStatusText = (status: ApplyType['status']): string => {
    return taskStatusMap.get(status!)?.text ?? '未知';
  };
  return {
    getStatusColor,
    getStatusText,
    getTaskStatusColor,
    getTaskStatusText,
  }
}
