stages:
  - test
  - build

variables:
  GIT_SUBMODULE_STRATEGY: recursive   # 自动拉取子模块
  IMAGE_NAME: "swr.cn-north-4.myhuaweicloud.com/iowork/work_app"

.default_docker:
  before_script:
    - echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin $CI_REGISTRY
  allow_failure: false

build_master:
  stage: build
  extends: .default_docker
  script:
    - docker build -t $IMAGE_NAME:canary .
    - docker push $IMAGE_NAME:canary
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'

build_beta:
  stage: build
  extends: .default_docker
  script:
    - docker build -t $IMAGE_NAME:beta .
    - docker push $IMAGE_NAME:beta
  rules:
    - if: '$CI_COMMIT_BRANCH == "beta"'

build_stable:
  stage: build
  extends: .default_docker
  script:
    - docker build -t $IMAGE_NAME:stable .
    - docker push $IMAGE_NAME:stable
  rules:
    - if: '$CI_COMMIT_BRANCH == "stable"'
