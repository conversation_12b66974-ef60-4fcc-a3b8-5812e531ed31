/**
 * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially useful
 * for Docker builds.
 */
import "./src/env.js";
import createNextIntlPlugin from 'next-intl/plugin';

/** @type {import("next").NextConfig} */
const config = {
  output: "standalone",
  images: {
    domains: ['localhost', '127.0.0.1', 'imgapi.xl0408.top', 's3-imfile.feishucdn.com'], // 允许的图片源域名
  },
};

const withNextIntl = createNextIntlPlugin();
export default withNextIntl(config)
