{
    "i18n-ally.enabledFrameworks": [
        "custom"
    ], // * This is the fix for the getTranslations issue
    "i18n-ally.localesPaths": [
        "./i18n/messages"
    ], // Change it to match your folder with all the JSON messages
    "i18n-ally.sourceLanguage": "zh", // Language that will replace the key on the file
    "i18n-ally.sortKeys": true,
    "i18n-ally.keystyle": "nested",
    "postman.settings.dotenv-detection-notification-visibility": false, // On key edit through the plugin, sort the JSON
}
