FROM node:22-alpine AS builder
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY prisma/ package.json package-lock.json ./
RUN npm ci --include=dev

COPY . .

ENV NEXT_TELEMETRY_DISABLED=1
ENV SKIP_ENV_VALIDATION=1
RUN npm run build

FROM node:22-alpine AS runner
WORKDIR /app

COPY --from=builder /app/next.config.js ./
COPY --from=builder /app/public ./public
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

ENV NODE_ENV=production
EXPOSE 3000

ENTRYPOINT ["node", "/app/server.js"]